// controllers/userController.js
import User from '../models/user.model.js';
import Solution from '../models/solution.model.js';
import Question from '../models/question.model.js';

// Step 1: Create or Update Question document (create the question document with your leetcode api fetched results)
export const handleQuestionEntry = async (req, res) => {
  try {
    const { title, questionLink, difficulty, tags } = req.body;

    let question = await Question.findOne({ title });

    if (!question) {
      question = await Question.create({
        title,
        questionLink,
        difficulty,
        tags,
        userList: []
      });
    }

    res.status(200).json({ success: true, question });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// Step 2: Submit Solution
export const submitSolution = async (req, res) => {
  try {
    //userid will be fetched from the cookie
    // questionTitle will be fetched from the request body
    const { userId, questionTitle, code, language } = req.body;

    const user = await User.findById(userId);
    if (!user) return res.status(404).json({ success: false, message: 'User not found' });

    const question = await Question.findOne({ title: questionTitle });
    if (!question) return res.status(404).json({ success: false, message: 'Question not found' });

    // Step 2(i): Create solution doc
    const newSolution = await Solution.create({
      user: user._id,
      question: question._id,
      code,
      language
    });

    // Step 2(ii): Append user to question's userList if not already present
    if (!question.userList.includes(user._id)) {
      question.userList.push(user._id);
      await question.save();
    }

    // Step 2(iii): Update user's sol map
    const difficultyKey = question.difficulty.toLowerCase();
    if (!user.sol) {
      user.sol = new Map();
    }

    if (!user.sol.get(difficultyKey)) {
      user.sol.set(difficultyKey, []);
    }

    user.sol.get(difficultyKey).push(newSolution._id);

    await user.save();

    res.status(201).json({ success: true, solution: newSolution });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

// akshat ->