/**
 * Solution controller
 * Handles HTTP requests for solution-related endpoints
 */
import asyncHandler from 'express-async-handler';
import { StatusCodes } from 'http-status-codes';
import Solution from '../models/solution.model.js';
import User from '../models/user.model.js';
import Question from '../models/question.model.js';

/**
 * Save a solution submission to the database
 * @route POST /api/v1/solutions
 * @access Private (requires authentication)
 */
const saveSolution = asyncHandler(async (req, res) => {
  const {
    questionTitle,
    questionLink,
    difficulty,
    tags,
    code,
    language,
    approach
  } = req.body;

  // Get user from request (set by auth middleware)
  let userId, user;

  if (req.user && req.user.id) {
    userId = req.user.id;
    user = await User.findById(userId);
  } else {
    res.status(StatusCodes.UNAUTHORIZED);
    throw new Error('Authentication required to save solution');
  }

  if (!user) {
    res.status(StatusCodes.NOT_FOUND);
    throw new Error('User not found');
  }

  // Find or create the question
  let question = await Question.findOne({ title: questionTitle });

  if (!question) {
    question = await Question.create({
      title: questionTitle,
      questionLink: questionLink,
      difficulty: difficulty.toLowerCase(),
      tags: tags || [],
      userList: [userId]
    });
  } else {
    // Add user to question's userList if not already present
    if (!question.userList.includes(userId)) {
      question.userList.push(userId);
      await question.save();
    }
  }

  // Check if solution already exists for this user and question
  const existingSolution = await Solution.findOne({
    user: userId,
    question: question._id
  });

  if (existingSolution) {
    res.status(StatusCodes.CONFLICT);
    throw new Error('Solution already exists for this question');
  }

  // Create new solution
  const solution = await Solution.create({
    user: userId,
    question: question._id,
    code,
    language,
    approach
  });

  // Update user's sol map with the new solution
  const difficultyKey = difficulty.toLowerCase();
  if (!user.sol) {
    user.sol = new Map();
  }

  if (!user.sol.get(difficultyKey)) {
    user.sol.set(difficultyKey, []);
  }

  user.sol.get(difficultyKey).push(solution._id);
  await user.save();

  res.status(StatusCodes.CREATED).json({
    success: true,
    message: 'Solution saved successfully',
    solution: {
      id: solution._id,
      questionTitle: question.title,
      difficulty: question.difficulty,
      language: solution.language,
      createdAt: solution.createdAt
    }
  });
});

/**
 * Get all solutions for a specific question (leaderboard)
 * @route GET /api/v1/solutions/problem/:questionId
 * @access Public
 */
const getSolutionsByProblem = asyncHandler(async (req, res) => {
  const { problemId } = req.params;
  const { limit = 50, page = 1 } = req.query;

  let question;

  // Try to find question by MongoDB ObjectId first
  try {
    question = await Question.findById(problemId);
  } catch (error) {
    // If not a valid ObjectId, try to find by title or other identifier
    question = null;
  }

  // If not found by ID, try to find by title (for backward compatibility)
  if (!question) {
    // For now, return empty results if question not found
    // In a real implementation, you might want to search by title or other fields
    return res.status(StatusCodes.OK).json({
      success: true,
      count: 0,
      solutions: []
    });
  }

  // Get all solutions for this question, sorted by submission time (first to solve gets rank 1)
  const solutions = await Solution.find({
    question: question._id
  })
  .populate('user', 'fullName username')
  .populate('question', 'title difficulty')
  .sort({
    createdAt: 1 // Earlier submissions first (first to solve gets rank 1)
  })
  .limit(parseInt(limit))
  .skip((parseInt(page) - 1) * parseInt(limit));

  // Format solutions for frontend (ranked by submission time - first to solve gets rank 1)
  const formattedSolutions = solutions.map((solution, index) => ({
    id: solution._id,
    username: solution.user?.username || solution.user?.fullName || 'Unknown User',
    code: solution.code,
    language: solution.language,
    approach: solution.approach,
    timestamp: solution.createdAt,
    problemId: solution.question._id,
    problemTitle: solution.question.title,
    stats: {
      runtime: 'N/A', // Not available in new model
      memory: 'N/A',  // Not available in new model
      runtimePercentile: null,
      memoryPercentile: null
    },
    rank: index + 1 // Rank based on submission order (first to solve = rank 1)
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get all solutions by a specific user
 * @route GET /api/v1/solutions/user/:userId
 * @access Public
 */
const getSolutionsByUser = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { limit = 20, page = 1 } = req.query;

  const solutions = await Solution.find({ user: userId })
    .populate('user', 'fullName username')
    .populate('question', 'title difficulty questionLink')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

  const formattedSolutions = solutions.map(solution => ({
    id: solution._id,
    questionTitle: solution.question.title,
    questionLink: solution.question.questionLink,
    difficulty: solution.question.difficulty,
    language: solution.language,
    code: solution.code,
    approach: solution.approach,
    timestamp: solution.createdAt
  }));

  res.status(StatusCodes.OK).json({
    success: true,
    count: formattedSolutions.length,
    solutions: formattedSolutions
  });
});

/**
 * Get solution statistics
 * @route GET /api/v1/solutions/stats
 * @access Public
 */
const getSolutionStats = asyncHandler(async (_req, res) => {
  const totalSolutions = await Solution.countDocuments();
  const uniqueUsers = await Solution.distinct('user').length;
  const uniqueQuestions = await Solution.distinct('question').length;

  // Get language distribution
  const languageStats = await Solution.aggregate([
    { $group: { _id: '$language', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]);

  res.status(StatusCodes.OK).json({
    success: true,
    stats: {
      totalSolutions,
      uniqueUsers,
      uniqueQuestions,
      languageDistribution: languageStats
    }
  });
});

export {
  saveSolution,
  getSolutionsByProblem,
  getSolutionsByUser,
  getSolutionStats
};
